// out: ../../style.css, compress: true, strictMath: true

/*
Theme Name: Limitless Agency
Author: <PERSON>
Version: 1.0.0 
*/   
   
@import 'vw_values.less';
@import 'constants.less';  
@import 'default.less';  
@import 'typo.less';   
@import 'parts/header.less';  
@import 'parts/artist.less';
@import 'parts/footer.less';  
@import 'parts/overlay.less';  
@import 'parts/ddsignature.less';
@import 'parts/cursor.less';
   
// blocks
@import '../../blocks/less/home-header-block.less';
@import '../../blocks/less/intro-text-block.less'; 
@import '../../blocks/less/artist-slider-block.less';
@import '../../blocks/less/artists-block.less';
@import '../../blocks/less/home-about-block.less';
@import '../../blocks/less/about-block.less';
@import '../../blocks/less/cta-block.less';
@import '../../blocks/less/booking-request-block.less';

// artists
@import '../../blocks/artist/css/artist-header-block.less';
@import '../../blocks/artist/css/artist-two-column-text-block.less';
@import '../../blocks/artist/css/artist-latest-release-block.less';
@import '../../blocks/artist/css/artist-big-slider-block.less';
@import '../../blocks/artist/css/artist-upcoming-shows-block.less';
@import '../../blocks/artist/css/artist-related-artists-block.less';
@import '../../blocks/artist/css/artist-media-block.less';

// APEX font-face declaraties
@font-face {
  font-family: 'ApexMk2-BoldExtended';
  src: url('../fonts/ApexMk2-BoldExtended.woff2') format('woff2'),
       url('../fonts/ApexMk2-BoldExtended.woff') format('woff'),
       url('../fonts/ApexMk2-BoldExtended.otf') format('opentype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'ApexMk2-LightCondensed';
  src: url('../fonts/ApexMk2-LightCondensed.woff2') format('woff2'),
       url('../fonts/ApexMk2-LightCondensed.woff') format('woff'),
       url('../fonts/ApexMk2-LightCondensed.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face { 
  font-family: 'ApexMk2-Regular';
  src: url('../fonts/ApexMk2-Regular.woff2') format('woff2'),
       url('../fonts/ApexMk2-Regular.woff') format('woff'),
       url('../fonts/ApexMk2-Regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'icomoon';
  src:  url('assets/fonts/icomoon.eot?twq3hi');
  src:  url('assets/fonts/icomoon.eot?twq3hi#iefix') format('embedded-opentype'),
    url('assets/fonts/icomoon.ttf?twq3hi') format('truetype'),
    url('assets/fonts/icomoon.woff?twq3hi') format('woff'),
    url('assets/fonts/icomoon.svg?twq3hi#icomoon') format('svg');
  font-weight: normal;
  font-style: normal; 
  font-display: block;
}  

[class^="icon-"], [class*=" icon-"] { 
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal; 
  text-transform: none; 
  line-height: 1; 

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} 
 
.icon-arrow-right-up:before {
  content: "\e900";
}
.icon-chevron-down:before {
  content: "\e901";
}
.icon-chevron-left:before {
  content: "\e902";
}
.icon-chevron-right:before {
  content: "\e903";
}
.icon-chevron-up:before {
  content: "\e904";
}
.icon-facebook:before {
  content: "\e905";
}
.icon-flame:before {
  content: "\e906";
}
.icon-globe:before {
  content: "\e907";
}
.icon-hardstyle:before {
  content: "\e908";
}
.icon-info:before {
  content: "\e909";
}
.icon-instagram:before {
  content: "\e90a";
}
.icon-limitless:before {
  content: "\e90b";
}
.icon-mail:before {
  content: "\e90c";
}
.icon-phone:before {
  content: "\e90d";
}
.icon-soundcloud:before {
  content: "\e90e";
}
.icon-spotify:before {
  content: "\e90f";
}
.icon-tiktok:before {
  content: "\e910";
}
.icon-whatsapp:before {
  content: "\e911";
}
.icon-x-twitter:before {
  content: "\e912";
}
.icon-youtube:before {
  content: "\e913";
}
.icon-linkedin:before {
  content: "\e914";
}
::-webkit-scrollbar {
  width: @vw10;
}

::-webkit-scrollbar-track {
  background: @almostWhite; 
}

::-webkit-scrollbar-thumb {
  border-radius: @vw50;
  background: rgba(0,0,0,.1);
}

.block__headline {
    padding: 20px 15px 30px;
    background: #fafafa;
    text-align: center;
}
.block__headline-title {
    font-family: 'Arial', sans-serif;
    font-size: 30px; 
    font-weight: bold;
    position: relative;
}
.block__headline-title:after {
    content: '';
    display: block;
    width: 40px; 
    height: 2px;
    background: #333;  
    margin: 0 auto;
}
 
html.has-scroll-smooth {
	backface-visibility: hidden;
	transform: translateZ(0);
  [data-load-container] {
  	position: fixed;
  	top: 0;
  	right: 0;
  	bottom: 0;
  	left: 0;
  	width: 100vw;
  }
}

// Swup

.transition-fade {
  transition: .75s;
  opacity: 1;
}

html.is-animating .transition-fade {
  opacity: 0;
}

.grecaptcha-badge {
  visibility: hidden;
}
 
@media all and (max-width: 1160px) { 

} 

@media all and (max-width: 580px) {
 
}

@keyframes dash-move {
  from {
    stroke-dashoffset: 0; /* Startpositie */
  }
  to {
    stroke-dashoffset: -20; /* Naar links verschuiven */
  }
}

.homeHeaderBlock .artistSlider .slide img {
  filter: url(#filter-glitch-3);
  transition: filter 0.2s cubic-bezier(.87,-0.41,.19,1.44);
  will-change: filter;
  display: block;
  width: 100%;
  height: auto;
  object-fit: cover;
}

// Zorg dat de filter niet zichtbaar is als er geen animatie is (optioneel, voor fallback)
@media not all and (min-resolution:.001dpcm) { @supports (-webkit-appearance:none) {
  .homeHeaderBlock .artistSlider .slide img {
    filter: none !important;
  }
}}