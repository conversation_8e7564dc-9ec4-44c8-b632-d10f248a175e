body.touch .footer .bigTitle a:hover {
  padding-left: 0;
  padding-right: 7.758vw;
  color: #191919 !important;
}
body.touch .footer .bigTitle a:hover:after {
  width: 0%;
}
body.touch .footer .bigTitle a:hover i {
  opacity: 0;
}
.footer {
  font-size: 1.042vw;
  background: #080036;
  padding: 4.051vw 0 1.157vw 0;
  color: #FFFFFF;
  position: relative;
  overflow: hidden;
}
.footer:after {
  z-index: 1;
}
.footer a {
  color: #FFFFFF;
  text-decoration: none;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.footer a:hover {
  opacity: 0.5;
}
.footer .footerTitle {
  text-transform: uppercase;
  font-size: 1.157vw;
  color: rgba(255, 255, 255, 0.4);
  margin-bottom: 1.157vw;
  font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif;
}
.footer .footerTitle i {
  color: #D4D0ED;
}
.footer p {
  font-size: 1.042vw;
}
.footer.inview .divider {
  -webkit-transform: scaleX(1);
  -moz-transform: scaleX(1);
  -o-transform: scaleX(1);
  -ms-transform: scaleX(1);
  transform: scaleX(1);
  -webkit-transition: transform 0.6s 0.45s cubic-bezier(0.85, 0, 0.15, 1);
  -moz-transition: transform 0.6s 0.45s cubic-bezier(0.85, 0, 0.15, 1);
  -o-transition: transform 0.6s 0.45s cubic-bezier(0.85, 0, 0.15, 1);
  transition: transform 0.6s 0.45s cubic-bezier(0.85, 0, 0.15, 1);
}
.footer .backgroundImage {
  position: absolute;
  top: 0%;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-mask-image: linear-gradient(rgba(0, 0, 0, 0), #000000, rgba(0, 0, 0, 0));
  mask-image: linear-gradient(rgba(0, 0, 0, 0), #000000, rgba(0, 0, 0, 0));
}
.footer .backgroundImage:before {
  height: 50%;
  position: absolute;
  top: 0;
  width: 100%;
  left: 0;
  pointer-events: none;
  z-index: 2;
  content: '';
  background: linear-gradient(0deg, rgba(8, 0, 54, 0), #080036);
}
.footer .backgroundImage:after {
  height: 50%;
  position: absolute;
  top: auto;
  bottom: 0;
  width: 100%;
  left: 0;
  pointer-events: none;
  z-index: 2;
  content: '';
  background: linear-gradient(180deg, rgba(8, 0, 54, 0), #080036);
}
.footer .backgroundImage img {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) scale(1);
  -moz-transform: translate(-50%, -50%) scale(1);
  -o-transform: translate(-50%, -50%) scale(1);
  -ms-transform: translate(-50%, -50%) scale(1);
  transform: translate(-50%, -50%) scale(1);
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.footer .socials {
  margin-top: 2.315vw;
}
.footer .socials .social {
  display: inline-block;
  height: 2.662vw;
  width: 2.662vw;
  cursor: pointer;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  text-decoration: none;
  color: #FFFFFF;
  line-height: 2.836vw;
  -webkit-transition: color 0.3s, background-color 0.3s;
  transition: color 0.3s, background-color 0.3s;
  text-align: center;
  font-size: 1.273vw;
}
.footer .socials .social:not(:last-child) {
  margin-right: 1.157vw;
}
.footer .socials .social:hover {
  background-color: #ffffff;
  color: #080036;
}
.footer .socials .social i {
  pointer-events: none;
}
.footer .logo {
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.footer .logo:hover {
  opacity: 0.5;
}
.footer .logo img {
  display: inline-block;
  height: 1.736vw;
  width: auto;
  margin-bottom: 4.051vw;
  object-fit: contain;
}
.footer ul {
  line-height: 1.5;
  list-style: none;
}
.footer ul li a {
  color: #FFFFFF;
  font-size: 1.042vw;
  text-decoration: none;
  cursor: pointer;
  -webkit-transition: color 0.3s 0s ease-out;
  -moz-transition: color 0.3s 0s ease-out;
  -o-transition: color 0.3s 0s ease-out;
  transition: color 0.3s 0s ease-out;
}
.footer ul li a:hover {
  color: #D4D0ED;
}
.footer .link {
  color: #D4D0ED;
  font-size: 1.042vw;
  display: inline-block;
  text-transform: lowercase;
  text-decoration: none;
  line-height: 1.4;
  cursor: pointer;
  position: relative;
}
.footer .link:hover:before,
.footer .link:hover:after {
  -webkit-transition: transform 0.3s 0s cubic-bezier(0.87, 0, 0.13, 1);
  -moz-transition: transform 0.3s 0s cubic-bezier(0.87, 0, 0.13, 1);
  -o-transition: transform 0.3s 0s cubic-bezier(0.87, 0, 0.13, 1);
  transition: transform 0.3s 0s cubic-bezier(0.87, 0, 0.13, 1);
}
.footer .link:hover:before {
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -o-transform: scaleX(0);
  -ms-transform: scaleX(0);
  transform: scaleX(0);
  transition-delay: 0s;
}
.footer .link:hover:after {
  -webkit-transform: scaleX(1);
  -moz-transform: scaleX(1);
  -o-transform: scaleX(1);
  -ms-transform: scaleX(1);
  transform: scaleX(1);
  transition-delay: 0.15s;
}
.footer .link:before,
.footer .link:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: auto;
  transform-origin: right;
  right: 0;
  background: #D4D0ED;
  -webkit-transform: scaleX(1);
  -moz-transform: scaleX(1);
  -o-transform: scaleX(1);
  -ms-transform: scaleX(1);
  transform: scaleX(1);
  height: 1px;
  width: 100%;
}
.footer .link:after {
  left: 0;
  right: auto;
  transform-origin: left;
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -o-transform: scaleX(0);
  -ms-transform: scaleX(0);
  transform: scaleX(0);
}
.footer .link:not(:last-child) {
  margin-right: 0.289vw;
}
.footer .topFooter .cols .col:first-child {
  width: calc(50% - 0.926vw);
}
.footer .footerContent {
  position: relative;
}
.footer .footerContent .imageCursor {
  position: absolute;
  height: 6.829vw;
  width: 13.194vw;
  top: 0;
  left: 0;
  pointer-events: none;
  opacity: 0;
  overflow: hidden;
}
.footer .footerContent .imageCursor img {
  top: 0;
  left: 0;
  object-fit: cover;
  object-position: center;
  position: absolute;
  width: 100%;
  height: 100%;
}
.footer .cols .col {
  display: inline-block;
  vertical-align: top;
  margin: 0 0.463vw;
  padding-right: 7.551vw;
  width: calc(25% - 0.926vw);
}
.footer .bottomFooter {
  color: #191919;
  margin-top: 1.273vw;
}
.footer .bottomFooter .col {
  display: inline-block;
  vertical-align: middle;
  width: 50%;
}
.footer .bottomFooter .col:last-child {
  text-align: right;
}
.footer .bottomFooter .menu {
  opacity: 0.7;
}
.footer .bottomFooter .menu li {
  display: inline-block;
}
.footer .bottomFooter .menu a {
  display: inline-block;
  vertical-align: middle;
  padding: 0.579vw;
  cursor: pointer;
  color: #FFFFFF;
  text-decoration: none;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.footer .bottomFooter .menu a:not(:last-of-type) {
  margin-right: 1.273vw;
}
.footer .bottomFooter .menu a:hover {
  color: #FFFFFF;
  opacity: 0.5;
}
.footer .bottomFooter .logo {
  width: 1.389vw;
  display: inline-block;
  vertical-align: middle;
}
.footer .bottomFooter .logo svg {
  object-fit: contain;
  width: 100%;
  height: auto;
}
.footer .divider {
  margin: 2.546vw 0;
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -o-transform: scaleX(0);
  -ms-transform: scaleX(0);
  transform: scaleX(0);
  transform-origin: left;
  height: 1px;
  background: #D4D0ED;
}
.footer .middleFooter .cols .col {
  padding-right: 2.315vw;
}
@media all and (max-width: 1160px) {
  .footer {
    padding: 6.034vw 0 1.724vw 0;
  }
  .footer .cols {
    margin-left: -1.724vw;
    width: calc(100% + 3.448vw);
  }
  .footer .cols .col {
    margin: 0 1.724vw;
    width: calc(33.3333% - 3.448vw);
  }
  .footer img {
    width: 12.931vw;
    margin-top: 3.448vw;
  }
  .footer .bigTitle a {
    padding-right: 3.879vw;
    padding-bottom: 0.862vw;
    padding-left: 0;
  }
  .footer .bigTitle a:after {
    height: 2px;
  }
  .footer .bigTitle a:hover {
    padding-left: 3.879vw;
  }
  .footer .bigTitle a i {
    left: -2.586vw;
  }
  .footer .logo {
    width: 2.069vw;
  }
  .footer .innerMenu {
    margin-left: 6.897vw;
  }
  .footer .innerMenu a {
    padding: 0.862vw;
  }
  .footer .innerMenu a:not(:last-of-type) {
    margin-right: 1.897vw;
  }
  .footer .bottomFooter {
    margin-top: 1.897vw;
  }
}
@media all and (max-width: 580px) {
  .footer {
    padding: 12.069vw 0 3.448vw 0;
  }
  .footer .backgroundWrapper {
    width: 120vw;
    height: 120vw;
  }
  .footer .cols {
    margin-left: -3.448vw;
    width: calc(100% + 6.897vw);
  }
  .footer .cols .col {
    margin: 0 3.448vw;
    width: calc(50% - 6.897vw);
  }
  .footer .cols .col:nth-child(2) {
    display: none;
  }
  .footer img {
    width: 25.861vw;
    margin-top: 6.897vw;
  }
  .footer .bigTitle a {
    padding-right: 7.758vw;
    padding-bottom: 1.724vw;
    padding-left: 0;
  }
  .footer .bigTitle a:after {
    height: 2px;
  }
  .footer .bigTitle a:hover {
    padding-left: 7.758vw;
  }
  .footer .bigTitle a i {
    left: -5.172vw;
  }
  .footer .logo {
    width: 4.137vw;
  }
  .footer .bottomFooter {
    margin-top: 8.62vw;
    font-size: 3.793vw;
  }
  .footer .innerMenu {
    display: none;
  }
}
@keyframes moveBackground {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(10vw, -5vw);
  }
  50% {
    transform: translate(-10vw, 5vw);
  }
  75% {
    transform: translate(5vw, -10vw);
  }
  100% {
    transform: translate(0, 0);
  }
}
