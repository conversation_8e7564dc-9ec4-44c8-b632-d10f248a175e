<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$category = get_field('category', $artist_id);
$args = [
    'post_type' => 'artist',
    'posts_per_page' => 4,
    'post_status' => 'publish',
    'post__not_in' => [$artist_id],
    'orderby' => 'rand',
    'meta_query' => [
        [
            'key' => 'image',
            'compare' => 'EXISTS',
        ],
    ],
];
if ($category) {
    $args['meta_query'] = [
        [
            'key' => 'category',
            'value' => $category,
            'compare' => '=',
        ]
    ];
}
$related = new WP_Query($args);
if ($related->have_posts()): ?>
    <section class="artistRelatedArtistsBlock artistSliderBlock">
        <div class="contentWrapper">
            <div class="cols">
                <div class="col">
                    <h2 class="normalTitle">Related artists</h2>
                </div>
                <div class="col">
                    <a href="<?= esc_url(get_post_type_archive_link('artist')) ?>" class="textLink backToArtists">
                        <span class="innerText">All artists</span>
                        <span class="arrows"><i class="icon-chevron-right"></i><i class="icon-chevron-right"></i></span>
                    </a>
                </div>
            </div>
            <?php while ($related->have_posts()): $related->the_post();
            setup_postdata($post);
            $title = get_the_title($post->ID);
            $link = get_the_permalink($post->ID);
            $image = get_field('image', $post->ID);
            ?>
            <a class="artist partial" title="<?php the_title(); ?>" href="<?php the_permalink(); ?>">
                <img class="lazy" data-src="<?php echo($image['sizes']['medium_large']); ?>" alt="<?php the_title(); ?>"/>
                <span class="smallTitle"><?php the_title(); ?></span>
                <span class="arrows"><i class="icon-arrow-right-up"></i><i class="icon-arrow-right-up"></i></span>
            </a>
            <?php endwhile; wp_reset_postdata(); ?>
        </div>
    </section>
<?php endif; ?>
