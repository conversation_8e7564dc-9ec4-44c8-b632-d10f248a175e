@import '../../../assets/less/constants.less';
@import '../../../assets/less/vw_values.less';

.artistMediaBlock {
  padding: @vw80 0;
  background: transparent;
  position: relative;
  &.inview {
    .mediaContainer {
      .youtubeContainer, .spotifyContainer {
        opacity: 1;
        .transform(translateY(0));
        transition: opacity 0.6s 0.2s ease-out, transform 0.6s 0.2s ease-out;
        .stagger(2, 0.15s);
      }
    }
  }
  .normalTitle {
    text-align: left; 
    margin-bottom: @vw26;
  }

  .mediaContainer {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: @vw40;
    align-items: flex-start;
  }
  .youtubeContainer, .spotifyContainer {
    opacity: 0;
    .transform(translateY(@vw50));
  }
  .youtubeContainer {
    .videoWrapper {
      position: relative;
      width: 100%;
      height: 0;
      padding-bottom: 56.25%; // 16:9 aspect ratio
      overflow: hidden;

      iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
      }
    }
  }
  
  .spotifyContainer {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; // Same 16:9 aspect ratio as YouTube
    overflow: hidden;

    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: none;
    }
  }
  
  .noMediaMessage {
    text-align: center;
    padding: @vw40;
    background: rgba(255, 255, 255, 0.05);
    .rounded(@vw15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    p {
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: @vw10;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      small {
        font-size: @vw14;
        opacity: 0.8;
      }
    }
  }
  
  // Responsive adjustments
  @media all and (max-width: 1160px) {
    padding: @vw60 0;

    .mediaContainer {
      gap: @vw30;
    }

    .mediaTitle {
      font-size: @vw28;
      margin-bottom: @vw40;
    }
  }

  @media all and (max-width: 580px) {
    padding: @vw40 0;

    .mediaTitle {
      font-size: @vw24;
      margin-bottom: @vw30;
    }

    .mediaContainer {
      grid-template-columns: 1fr;
      gap: @vw20;
    }

    .spotifyContainer {
      height: 300px;
    }
  }
  
  // Custom scrollbar for Spotify iframe (if visible)
  .spotifyContainer iframe {
    scrollbar-width: thin;
    scrollbar-color: @secondaryColor rgba(255, 255, 255, 0.1);
    
    &::-webkit-scrollbar {
      width: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      .rounded(4px);
    }
    
    &::-webkit-scrollbar-thumb {
      background: @secondaryColor;
      .rounded(4px);
      
      &:hover {
        background: @secondaryColorLight;
      }
    }
  }
}
