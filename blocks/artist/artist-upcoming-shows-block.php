UPCOMING SHOWS BLOCK.

<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$bandsintown_id = get_field('bandsintown_id', $artist_id);
if ($bandsintown_id):
    $events = get_artist_bandsintown_events($bandsintown_id);
    if ($events && is_array($events) && count($events)) {
        // Controleer of het resultaat een foutmelding is (Bandsintown API geeft soms een 'errors' key terug)
        if (isset($events['errors']) || isset($events['error'])) {
            $events = [];
        }
    }
    if ($events && is_array($events) && count($events)): ?>
        <section class="artistUpcomingShowsBlock">
            <h2 class="ArtistUpcomingShowsBlock__Title">Aankomende shows</h2>
            <div class="ArtistUpcomingShowsBlock__List">
                <?php foreach ($events as $event): ?>
                    <?php if (is_array($event) && isset($event['datetime'], $event['venue']) && is_array($event['venue'])): ?>
                        <div class="ArtistUpcomingShowsBlock__Item">
                            <span class="ArtistUpcomingShowsBlock__Date"><?= esc_html(date('d-m-Y', strtotime($event['datetime']))) ?></span>
                            <span class="ArtistUpcomingShowsBlock__Venue"><?= esc_html($event['venue']['name'] ?? '') ?>, <?= esc_html($event['venue']['city'] ?? '') ?></span>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </section>
    <?php elseif ($events !== false): ?>
        <section class="artistUpcomingShowsBlock">
            <h2 class="ArtistUpcomingShowsBlock__Title">Aankomende shows</h2>
            <div class="ArtistUpcomingShowsBlock__List">
                <div class="ArtistUpcomingShowsBlock__Item">Geen shows gepland.</div>
            </div>
        </section>
    <?php endif;
endif;
