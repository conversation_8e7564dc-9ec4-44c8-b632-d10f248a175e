//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';

.artistUpcomingShowsBlock {
  margin: @vw100 * 2 0;
  padding: @vw60 0;

  &.inview {
    .upcomingShowsTitle {
      .transform(translateY(0));
      opacity: 1;
      transition: opacity 0.6s 0.2s ease-out, transform 0.6s 0.2s ease-out;
    }

    .showItem {
      .transform(translateY(0));
      opacity: 1;
      transition: opacity 0.8s ease-out, transform 0.8s ease-out;
      .stagger(10, 0.1s, 0.4s);
    }
  }

  .upcomingShowsTitle {
    font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif;
    font-size: @vw32;
    font-weight: bold;
    color: @hardWhite;
    text-align: center;
    margin-bottom: @vw60;
    letter-spacing: 2px;
    .transform(translateY(@vw30));
    opacity: 0;
  }

  .bandsintownWidget {
    max-width: 800px;
    margin: 0 auto;

    // Base styling voor de widget container
    .bit-widget-initializer {
      display: block;
      width: 100%;
    }
  }

  .widgetFallback {
    max-width: 800px;
    margin: 0 auto;

    .noEventsMessage {
      text-align: center;
      padding: @vw40;
      background: rgba(255, 255, 255, 0.05);
      .rounded(@vw15);
      border: 1px solid rgba(255, 255, 255, 0.1);

      p {
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: @vw10;

        &:last-child {
          margin-bottom: 0;
        }

        a {
          color: @secondaryColor;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }

  .showsList {
    display: flex;
    flex-direction: column;
    gap: @vw20;
    max-width: 800px;
    margin: 0 auto;

    .showItem {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      .rounded(@vw15);
      padding: @vw25 @vw30;
      transition: all 0.3s ease;
      .transform(translateY(@vw30));
      opacity: 0;

      &:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(255, 255, 255, 0.2);
        .transform(translateY(-2px));

        .ticketsButton {
          background: @secondaryColorLight;
          color: @primaryColor;
          .transform(scale(1.05));
        }
      }

      .showInfo {
        display: flex;
        align-items: center;
        gap: @vw40;
        flex: 1;

        .showDate {
          font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif;
          font-size: @vw24;
          font-weight: bold;
          color: @hardWhite;
          min-width: 80px;
          text-align: left;
        }

        .showDetails {
          flex: 1;

          .showLocation {
            font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif;
            font-size: @vw18;
            font-weight: bold;
            color: @secondaryColorLight;
            margin-bottom: @vw5;
            letter-spacing: 1px;
          }

          .showVenue {
            font-family: 'ApexMk2-Regular', Arial, sans-serif;
            font-size: @vw16;
            font-weight: normal;
            color: rgba(255, 255, 255, 0.8);
            letter-spacing: 0.5px;
          }
        }
      }

      .showTickets {
        .ticketsButton {
          display: inline-block;
          background: @secondaryColor;
          color: @hardWhite;
          padding: @vw12 @vw25;
          .rounded(@vw8);
          text-decoration: none;
          font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif;
          font-size: @vw14;
          font-weight: bold;
          letter-spacing: 1px;
          transition: all 0.3s ease;
          cursor: pointer;

          &:hover {
            background: @secondaryColorLight;
            color: @primaryColor;
            .transform(scale(1.05));
          }
        }
      }
    }

    .noEventsMessage {
      text-align: center;
      padding: @vw40;
      background: rgba(255, 255, 255, 0.05);
      .rounded(@vw15);
      border: 1px solid rgba(255, 255, 255, 0.1);

      p {
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: @vw10;

        &:last-child {
          margin-bottom: 0;
        }

        small {
          color: rgba(255, 255, 255, 0.5);
          font-size: @vw12;
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .showsList {
      .showItem {
        flex-direction: column;
        align-items: flex-start;
        gap: @vw20;

        .showInfo {
          width: 100%;
          flex-direction: column;
          align-items: flex-start;
          gap: @vw15;

          .showDate {
            min-width: auto;
          }
        }

        .showTickets {
          align-self: stretch;

          .ticketsButton {
            display: block;
            text-align: center;
            width: 100%;
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .upcomingShowsTitle {
      font-size: @vw28;
    }

    .showsList {
      .showItem {
        padding: @vw20;

        .showInfo {
          .showDate {
            font-size: @vw20;
          }

          .showDetails {
            .showLocation {
              font-size: @vw16;
            }

            .showVenue {
              font-size: @vw14;
            }
          }
        }
      }
    }
  }
}
