<?php
// Add security headers for media embeds
add_action('wp_head', function() {
    if (is_singular('artist')) {
        echo '<meta http-equiv="Content-Security-Policy" content="frame-src https://www.youtube.com https://open.spotify.com; media-src https:; script-src \'self\' \'unsafe-inline\' https:;">' . "\n";
    }
});

$artist_id = $args['artist_id'] ?? get_the_ID();
$artist_name = get_the_title($artist_id);

// Get custom fields
$youtube_url = get_field('youtube_url', $artist_id);
$spotify_url = get_field('spotify_url', $artist_id);

// Extract YouTube video ID from URL
$youtube_video_id = '';
if ($youtube_url) {
    $youtube_video_id = extract_youtube_video_id($youtube_url);
}

// Extract Spotify embed URL from URL
$spotify_embed_url = '';
if ($spotify_url) {
    $spotify_embed_url = convert_spotify_url_to_embed($spotify_url);
}

/**
 * Extract YouTube video ID from various YouTube URL formats
 */
function extract_youtube_video_id($url) {
    // Force HTTPS for security
    $url = str_replace('http://', 'https://', $url);

    $patterns = [
        '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/',
        '/youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/',
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1];
        }
    }

    return '';
}

/**
 * Convert Spotify URL to embed URL
 */
function convert_spotify_url_to_embed($url) {
    // Remove query parameters and extract the important part
    $url = strtok($url, '?');

    // Force HTTPS for security
    $url = str_replace('http://', 'https://', $url);

    // Convert open.spotify.com to embed format
    if (strpos($url, 'open.spotify.com') !== false) {
        $embed_url = str_replace('open.spotify.com', 'open.spotify.com/embed', $url);
        return $embed_url . '?utm_source=generator&theme=0';
    }

    return '';
}


?>

<section class="artistMediaBlock" data-init>
    <div class="contentWrapper smaller">
        <h2 class="normalTitle">POPULAR RELEASES</h2>
        
        <div class="mediaContainer">
            <?php if ($youtube_video_id): ?>
                <div class="youtubeContainer">
                    <div class="videoWrapper">
                        <iframe
                            src="https://www.youtube.com/embed/<?= esc_attr($youtube_video_id) ?>?rel=0&showinfo=0&modestbranding=1&fs=0"
                            frameborder="0"
                            allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen>
                        </iframe>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($spotify_embed_url): ?>
                <div class="spotifyContainer">
                    <iframe
                        src="<?= esc_url($spotify_embed_url) ?>"
                        width="100%"
                        height="352"
                        frameborder="0"
                        allowfullscreen=""
                        allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture"
                        referrerpolicy="strict-origin-when-cross-origin"
                        loading="lazy">
                    </iframe>

                    <!-- Fallback link if iframe fails -->
                    <noscript>
                        <div class="spotify-fallback">
                            <a href="<?= esc_url($spotify_url) ?>" target="_blank" class="button">
                                <span class="innerText">Open on Spotify</span>
                                <span class="arrows">
                                    <i class="icon-arrow-right-up"></i>
                                    <i class="icon-arrow-right-up"></i>
                                </span>
                            </a>
                        </div>
                    </noscript>
                </div>
            <?php endif; ?>
        </div>

        <?php if (!$youtube_video_id && !$spotify_embed_url && current_user_can('edit_posts')): ?>
            <div class="noMediaMessage">
                <p>No media configured for this artist.</p>
                <p><small>Add YouTube URL and Spotify URL in the artist edit page.</small></p>
            </div>
        <?php endif; ?>
    </div>
</section>
