<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$artist_name = get_the_title($artist_id);

// Get custom fields
$youtube_channel_id = get_field('youtube_channel_id', $artist_id);
$youtube_video_id = get_field('featured_youtube_video', $artist_id); // Manual override
$spotify_playlist_id = get_field('spotify_playlist_id', $artist_id);
$spotify_artist_id = get_field('spotify_artist_id', $artist_id);

// YouTube API key (add to wp-config.php: define('YOUTUBE_API_KEY', 'your-key');)
$youtube_api_key = defined('YOUTUBE_API_KEY') ? YOUTUBE_API_KEY : '';

// Get most popular YouTube video if no manual override
if (!$youtube_video_id && $youtube_channel_id && $youtube_api_key) {
    $youtube_video_id = get_most_popular_youtube_video($youtube_channel_id, $youtube_api_key);
}

// Fallback: use manual video ID or default
if (!$youtube_video_id) {
    $youtube_video_id = 'dQw4w9WgXcQ'; // Default fallback
}

// Get Spotify embed URL
$spotify_embed_url = '';
if ($spotify_playlist_id) {
    $spotify_embed_url = "https://open.spotify.com/embed/playlist/{$spotify_playlist_id}";
} elseif ($spotify_artist_id) {
    $spotify_embed_url = "https://open.spotify.com/embed/artist/{$spotify_artist_id}";
}

function get_most_popular_youtube_video($channel_id, $api_key) {
    $cache_key = 'youtube_popular_' . $channel_id;
    $cached_video = get_transient($cache_key);
    
    if ($cached_video !== false) {
        return $cached_video;
    }
    
    // Get channel uploads playlist
    $channel_url = "https://www.googleapis.com/youtube/v3/channels?part=contentDetails&id={$channel_id}&key={$api_key}";
    $channel_response = wp_remote_get($channel_url);
    
    if (is_wp_error($channel_response)) {
        return false;
    }
    
    $channel_data = json_decode(wp_remote_retrieve_body($channel_response), true);
    
    if (!isset($channel_data['items'][0]['contentDetails']['relatedPlaylists']['uploads'])) {
        return false;
    }
    
    $uploads_playlist_id = $channel_data['items'][0]['contentDetails']['relatedPlaylists']['uploads'];
    
    // Get recent videos from uploads playlist
    $videos_url = "https://www.googleapis.com/youtube/v3/playlistItems?part=snippet&playlistId={$uploads_playlist_id}&maxResults=10&key={$api_key}";
    $videos_response = wp_remote_get($videos_url);
    
    if (is_wp_error($videos_response)) {
        return false;
    }
    
    $videos_data = json_decode(wp_remote_retrieve_body($videos_response), true);
    
    if (!isset($videos_data['items']) || empty($videos_data['items'])) {
        return false;
    }
    
    // Get video IDs
    $video_ids = array();
    foreach ($videos_data['items'] as $item) {
        $video_ids[] = $item['snippet']['resourceId']['videoId'];
    }
    
    // Get video statistics
    $stats_url = "https://www.googleapis.com/youtube/v3/videos?part=statistics&id=" . implode(',', $video_ids) . "&key={$api_key}";
    $stats_response = wp_remote_get($stats_url);
    
    if (is_wp_error($stats_response)) {
        return $video_ids[0]; // Return first video as fallback
    }
    
    $stats_data = json_decode(wp_remote_retrieve_body($stats_response), true);
    
    if (!isset($stats_data['items'])) {
        return $video_ids[0];
    }
    
    // Find most popular video
    $most_popular_video = '';
    $highest_views = 0;
    
    foreach ($stats_data['items'] as $video) {
        $views = intval($video['statistics']['viewCount'] ?? 0);
        if ($views > $highest_views) {
            $highest_views = $views;
            $most_popular_video = $video['id'];
        }
    }
    
    // Cache for 1 hour
    set_transient($cache_key, $most_popular_video, HOUR_IN_SECONDS);
    
    return $most_popular_video ?: $video_ids[0];
}
?>

<section class="artistMediaBlock" data-init>
    <div class="contentWrapper smaller">
        <h2 class="mediaTitle">POPULAR RELEASES</h2>
        
        <div class="mediaContainer">
            <?php if ($youtube_video_id): ?>
                <div class="youtubeContainer">
                    <div class="videoWrapper">
                        <iframe 
                            src="https://www.youtube.com/embed/<?= esc_attr($youtube_video_id) ?>?rel=0&showinfo=0&modestbranding=1" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                        </iframe>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if ($spotify_embed_url): ?>
                <div class="spotifyContainer">
                    <iframe 
                        src="<?= esc_url($spotify_embed_url) ?>?utm_source=generator&theme=0" 
                        width="100%" 
                        height="352" 
                        frameborder="0" 
                        allowfullscreen="" 
                        allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture" 
                        loading="lazy">
                    </iframe>
                </div>
            <?php endif; ?>
        </div>
        
        <?php if (!$youtube_video_id && !$spotify_embed_url && current_user_can('edit_posts')): ?>
            <div class="noMediaMessage">
                <p>No media configured for this artist.</p>
                <p><small>Add YouTube Channel ID, Video ID, or Spotify Playlist/Artist ID in the artist edit page.</small></p>
            </div>
        <?php endif; ?>
    </div>
</section>
