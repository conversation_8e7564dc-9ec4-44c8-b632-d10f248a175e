<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$logo = get_field('logo', $artist_id);
$category = get_field('category', $artist_id);
$name = get_the_title($artist_id);
$socials = [
    'instagram' => get_field('instagram', $artist_id),
    'tiktok' => get_field('tiktok', $artist_id),
    'youtube' => get_field('youtube', $artist_id),
    'spotify' => get_field('spotify', $artist_id),
    'soundcloud' => get_field('soundcloud', $artist_id),
    'facebook' => get_field('facebook', $artist_id),
    'twitter_x' => get_field('twitter_x', $artist_id),
    'hardstyle' => get_field('hardstyle', $artist_id),
];
$image = get_field('image', $artist_id);
$presskit = get_field('presskit', $artist_id);
$visual_pack = get_field('visual_pack', $artist_id);
$artist_managers = get_field('artist_managers', $artist_id);
?>
<section class="artistHeaderBlock" data-init>
    <? if ($image): ?>
        <div class="backgroundImage">
            <img src="<?= esc_url($image['url']) ?>" alt="<?= esc_attr($name) ?>" />
        </div>
    <?php endif; ?>
    <div class="contentWrapper smaller">
        <?php if ($logo): ?>
            <img class="artistLogo" src="<?= esc_url($logo['url']) ?>" alt="Logo <?= esc_attr($name) ?>" />
        <?php endif; ?>
        <?php if ($category): ?>
            <div class="ArtistGenre"><?= esc_html($category) ?></div>
        <?php endif; ?>
        <h1 class="hugeTitle" data-lines data-words data-parallax data-parallax-speed="-1"><?= esc_html($name) ?></h1>
        <div class="socials">
            <?php foreach ($socials as $key => $url):
                if ($url): ?>
                    <a href="<?= esc_url($url) ?>" target="_blank" rel="noopener" class="social -<?= esc_attr($key) ?>">
                        <i class="icon-<?= esc_attr($key) ?>"></i>
                    </a>
                <?php endif;
            endforeach; ?>
        </div>
        <a href="<?= esc_url(get_post_type_archive_link('artist')) ?>" class="textLink backToArtists">
            <span class="arrows"><i class="icon-chevron-left"></i><i class="icon-chevron-left"></i></span>
            <span class="innerText">&nbsp;All artists</span>
        </a>
        <div class="infoWrapper">
            <?php if ($image): ?>
                <div class="imageWrapper">
                    <div class="innerImage">
                        <img data-parallax data-parallax-speed="2" src="<?= esc_url($image['url']) ?>" alt="<?= esc_attr($name) ?>" />
                    </div>
                </div>
            <?php endif; ?>
             <div class="contactDetails">
                <h2 class="normalTitle">Contact</h2>
                <div class="text">
                    <p>
                    If you have any questions, feel free to reach out. We love a personal message.
                    </p>
                </div>
                <?php if ($artist_managers):
                    foreach ((array)$artist_managers as $manager_id):
                        $email = get_field('email', $manager_id);
                        if ($email): ?>
                            <a class="button" href="mailto:<?= antispambot($email) ?>">
                                <span class="innerText">Mail <?= get_the_title($manager_id) ?></span>
                                <span class="arrows">
                                    <i class="icon-arrow-right-up"></i>
                                    <i class="icon-arrow-right-up"></i>
                                </span>
                            </a>
                        <?php endif;
                    endforeach;
                endif; ?>
                <?php if ($presskit): ?>
                    <a class="button" href="<?= esc_url($presskit) ?>" target="_blank">
                        <span class="innerText">
                            Download Presskit
                        </span>
                        <span class="arrows">
                            <i class="icon-arrow-right-up"></i>
                            <i class="icon-arrow-right-up"></i>
                        </span>
                    </a>
                <?php endif; ?>
                <?php if ($visual_pack): ?>
                    <a class="button" href="<?= esc_url($visual_pack) ?>" target="_blank">
                        <span class="innerText">
                        Download Visual Pack
                        </span>
                        <span class="arrows">
                            <i class="icon-arrow-right-up"></i>
                            <i class="icon-arrow-right-up"></i>
                        </span>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
