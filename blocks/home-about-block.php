<?php
$background_image = get_field('background_image');
$background_video = get_field('background_video');
$image_1 = get_field('image_1');
$image_2 = get_field('image_2');
$image_3 = get_field('image_3');
$image_4 = get_field('image_4');
$image_5 = get_field('image_5');
$image_6 = get_field('image_6');
$image_7 = get_field('image_7');
$size = "medium_large";
?>
<section class="homeAboutBlock noMarginBottom" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
   <div class="stickyItem">
      <?php if ($background_video): ?>
        <video class="video" muted playsinline loop autoplay poster="<?php echo esc_url($background_image["sizes"]['medium_large']); ?>">
            <source src="<?php echo esc_url($background_video); ?>" type="video/mp4">
        </video>
      <?php elseif ($background_image): ?>
        <img class="lazy" data-src="<?php echo esc_url($background_image["sizes"]['large']); ?>" alt="<?php echo esc_attr($background_image['alt']); ?>" />
      <?php endif; ?>
      <div class="innerContent">
        <h2 class="bigTitle white"><?php the_field("title") ?></h2>
        <div class="subTitle white"><?php the_field("subtitle") ?></div>
        <div class="socials">
          <?php if(get_theme_mod('customTheme-main-callout-instagram')): ?>
            <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram', 'https://www.instagram.com')); ?>" title="instagram" target="_blank"><i class="icon-instagram"></i></a>
          <?php endif; ?>
          <?php if(get_theme_mod('customTheme-main-callout-tiktok')): ?>
            <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-tiktok', 'https://www.tiktok.com')); ?>" title="tiktok" target="_blank"><i class="icon-tiktok"></i></a>
          <?php endif; ?>
          <?php if(get_theme_mod('customTheme-main-callout-linkedin')): ?>
            <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin', 'https://www.linkedin.com')); ?>" title="linkedin" target="_blank"><i class="icon-linkedin"></i></a>
          <?php endif; ?>
          <?php if(get_theme_mod('customTheme-main-callout-facebook')): ?>
            <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook', 'https://www.facebook.com')); ?>" title="facebook" target="_blank"><i class="icon-facebook"></i></a>
          <?php endif; ?>
        </div>
      </div>
   </div>
   <div class="imageRows">
    <div class="imageRow">
        <div class="imageWrapper">
          <div class="innerImage">
            <img class="lazy" data-src="<?php echo esc_url($image_1["sizes"][$size]); ?>" alt="<?php echo esc_attr($image_1['alt']); ?>" />
          </div>
        </div>
        <div class="imageWrapper">
          <div class="innerImage">
            <img class="lazy" data-src="<?php echo esc_url($image_2["sizes"][$size]); ?>" alt="<?php echo esc_attr($image_3['alt']); ?>" />
          </div>
        </div>
        <div class="imageWrapper">
          <div class="innerImage">
            <img class="lazy" data-src="<?php echo esc_url($image_3["sizes"][$size]); ?>" alt="<?php echo esc_attr($image_3['alt']); ?>" />
          </div>
        </div>
    </div>
    <div class="imageRow">
        <div class="imageWrapper">
          <div class="innerImage">
            <img class="lazy" data-src="<?php echo esc_url($image_4["sizes"][$size]); ?>" alt="<?php echo esc_attr($image_4['alt']); ?>" />
          </div>
        </div>
        <div class="imageWrapper">
          <div class="innerImage">
            <img class="lazy" data-src="<?php echo esc_url($image_5["sizes"][$size]); ?>" alt="<?php echo esc_attr($image_5['alt']); ?>" />
          </div>
        </div>
    </div>
    <div class="imageRow">
        <div class="imageWrapper">
          <div class="innerImage">
            <img class="lazy" data-src="<?php echo esc_url($image_6["sizes"][$size]); ?>" alt="<?php echo esc_attr($image_7['alt']); ?>" />
          </div>
        </div>
        <div class="imageWrapper">
          <div class="innerImage">
            <img class="lazy" data-src="<?php echo esc_url($image_7["sizes"][$size]); ?>" alt="<?php echo esc_attr($image_7['alt']); ?>" />
          </div>
        </div>
    </div>
  </div>
</section>
