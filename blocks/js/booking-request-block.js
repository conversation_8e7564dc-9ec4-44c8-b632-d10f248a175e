$(document).ready(function () {
    $(document).on("initPage", function () {
        if ($(".bookingRequestBlock").length > 0) {
            initializeBookingRequestBlock();
        }
    });
});

function initializeBookingRequestBlock() {
    const $block = $('.bookingRequestBlock');
    const $steps = $block.find('.formStep');
    const $stepIndicators = $block.find('.stepIndicator');
    const $progressFill = $block.find('.progressFill');
    const $prevButton = $block.find('.prevButton');
    const $nextButton = $block.find('.nextButton');
    const $submitButton = $block.find('.submitButton');
    
    let currentStep = 1;
    const totalSteps = $steps.length;
    
    // Initialize
    updateProgressBar();
    updateNavigationButtons();
    
    // Navigation event handlers
    $nextButton.on('click', function() {
        if (validateCurrentStep()) {
            nextStep();
        }
    });
    
    $prevButton.on('click', function() {
        prevStep();
    });
    
    $submitButton.on('click', function() {
        submitForm();
    });
    
    // Step indicator click handlers
    $stepIndicators.on('click', function() {
        const targetStep = parseInt($(this).data('step'));
        if (targetStep < currentStep || validateStepsUpTo(targetStep - 1)) {
            goToStep(targetStep);
        }
    });
    
    // Artist selection handlers
    $block.find('.artistOption input[type="checkbox"]').on('change', function() {
        const $option = $(this).closest('.artistOption');
        if ($(this).is(':checked')) {
            $option.addClass('selected');
        } else {
            $option.removeClass('selected');
        }
        updateSelectedArtistsCount();
    });
    
    // Form field change handlers for real-time validation
    $block.find('input, select, textarea').on('change blur', function() {
        validateField($(this));
    });
    
    function nextStep() {
        if (currentStep < totalSteps) {
            currentStep++;
            updateStep();
        }
    }
    
    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            updateStep();
        }
    }
    
    function goToStep(step) {
        if (step >= 1 && step <= totalSteps) {
            currentStep = step;
            updateStep();
        }
    }
    
    function updateStep() {
        // Hide all steps
        $steps.removeClass('active');
        $stepIndicators.removeClass('active completed');
        
        // Show current step
        $steps.filter(`[data-step="${currentStep}"]`).addClass('active');
        $stepIndicators.filter(`[data-step="${currentStep}"]`).addClass('active');
        
        // Mark completed steps
        for (let i = 1; i < currentStep; i++) {
            $stepIndicators.filter(`[data-step="${i}"]`).addClass('completed');
        }
        
        // Update progress bar
        updateProgressBar();
        
        // Update navigation buttons
        updateNavigationButtons();
        
        // Update review summary if on last step
        if (currentStep === totalSteps) {
            updateReviewSummary();
        }
        
        // Animate step transition
        animateStepTransition();
    }
    
    function updateProgressBar() {
        const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
        gsap.to($progressFill, {
            width: `${progress}%`,
            duration: 0.5,
            ease: 'power2.out'
        });
    }
    
    function updateNavigationButtons() {
        // Previous button
        $prevButton.prop('disabled', currentStep === 1);
        
        // Next/Submit buttons
        if (currentStep === totalSteps) {
            $nextButton.hide();
            $submitButton.show();
        } else {
            $nextButton.show();
            $submitButton.hide();
        }
    }
    
    function animateStepTransition() {
        const $activeStep = $steps.filter('.active');
        
        // Animate step content in
        gsap.fromTo($activeStep.find('.stepTitle, .stepDescription'), {
            y: 30,
            opacity: 0
        }, {
            y: 0,
            opacity: 1,
            duration: 0.6,
            stagger: 0.1,
            ease: 'power2.out'
        });
        
        gsap.fromTo($activeStep.find('.formFields, .artistSelection, .reviewSummary'), {
            y: 40,
            opacity: 0
        }, {
            y: 0,
            opacity: 1,
            duration: 0.8,
            delay: 0.2,
            ease: 'power2.out'
        });
    }
    
    function validateCurrentStep() {
        switch (currentStep) {
            case 1:
                return validateArtistSelection();
            case 2:
                return validateEventDetails();
            case 3:
                return validateContactInfo();
            case 4:
                return true; // Optional step
            case 5:
                return true; // Review step
            default:
                return true;
        }
    }
    
    function validateStepsUpTo(step) {
        for (let i = 1; i <= step; i++) {
            currentStep = i;
            if (!validateCurrentStep()) {
                return false;
            }
        }
        return true;
    }
    
    function validateArtistSelection() {
        const selectedArtists = $block.find('.artistOption input[type="checkbox"]:checked');
        if (selectedArtists.length === 0) {
            showValidationError('Please select at least one artist.');
            return false;
        }
        return true;
    }
    
    function validateEventDetails() {
        const requiredFields = ['event_date', 'event_location', 'event_type'];
        let isValid = true;
        
        requiredFields.forEach(fieldName => {
            const $field = $block.find(`[name="${fieldName}"]`);
            if (!$field.val().trim()) {
                markFieldAsInvalid($field);
                isValid = false;
            } else {
                markFieldAsValid($field);
            }
        });
        
        if (!isValid) {
            showValidationError('Please fill in all required fields.');
        }
        
        return isValid;
    }
    
    function validateContactInfo() {
        const requiredFields = ['contact_name', 'contact_email'];
        let isValid = true;
        
        requiredFields.forEach(fieldName => {
            const $field = $block.find(`[name="${fieldName}"]`);
            if (!$field.val().trim()) {
                markFieldAsInvalid($field);
                isValid = false;
            } else {
                markFieldAsValid($field);
            }
        });
        
        // Validate email format
        const $emailField = $block.find('[name="contact_email"]');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if ($emailField.val() && !emailRegex.test($emailField.val())) {
            markFieldAsInvalid($emailField);
            isValid = false;
        }
        
        if (!isValid) {
            showValidationError('Please fill in all required fields with valid information.');
        }
        
        return isValid;
    }
    
    function validateField($field) {
        const value = $field.val().trim();
        const isRequired = $field.prop('required');
        
        if (isRequired && !value) {
            markFieldAsInvalid($field);
            return false;
        } else if (value) {
            markFieldAsValid($field);
            return true;
        }
        
        return true;
    }
    
    function markFieldAsInvalid($field) {
        $field.addClass('invalid').removeClass('valid');
        $field.closest('.fieldGroup').addClass('has-error');
    }
    
    function markFieldAsValid($field) {
        $field.addClass('valid').removeClass('invalid');
        $field.closest('.fieldGroup').removeClass('has-error');
    }
    
    function showValidationError(message) {
        // Remove existing error messages
        $block.find('.validation-error').remove();
        
        // Add new error message
        const $error = $('<div class="validation-error">' + message + '</div>');
        $block.find('.formNavigation').before($error);
        
        // Animate error message
        gsap.fromTo($error, {
            opacity: 0,
            y: -20
        }, {
            opacity: 1,
            y: 0,
            duration: 0.3
        });
        
        // Remove error after 5 seconds
        setTimeout(() => {
            gsap.to($error, {
                opacity: 0,
                y: -20,
                duration: 0.3,
                onComplete: () => $error.remove()
            });
        }, 5000);
    }
    
    function updateSelectedArtistsCount() {
        const count = $block.find('.artistOption input[type="checkbox"]:checked').length;
        const $counter = $block.find('.selected-artists-counter');
        
        if ($counter.length === 0 && count > 0) {
            const $counter = $('<div class="selected-artists-counter">' + count + ' artist(s) selected</div>');
            $block.find('.artistSelection').after($counter);
        } else if ($counter.length > 0) {
            if (count > 0) {
                $counter.text(count + ' artist(s) selected');
            } else {
                $counter.remove();
            }
        }
    }
    
    function updateReviewSummary() {
        // Selected Artists
        const selectedArtists = [];
        $block.find('.artistOption input[type="checkbox"]:checked').each(function() {
            selectedArtists.push($(this).data('artist-name'));
        });
        $block.find('.selectedArtistsList').html(selectedArtists.join(', ') || 'None selected');
        
        // Event Details
        const eventDetails = {
            'Date': $block.find('[name="event_date"]').val(),
            'Location': $block.find('[name="event_location"]').val(),
            'Venue': $block.find('[name="venue_name"]').val() || 'Not specified',
            'Type': $block.find('[name="event_type"] option:selected').text(),
            'Expected Attendance': $block.find('[name="expected_attendance"] option:selected').text() || 'Not specified'
        };
        
        let eventDetailsHtml = '';
        Object.entries(eventDetails).forEach(([key, value]) => {
            if (value && value !== 'Not specified') {
                eventDetailsHtml += `<p><strong>${key}:</strong> ${value}</p>`;
            }
        });
        $block.find('.eventDetailsSummary').html(eventDetailsHtml);
        
        // Contact Information
        const contactInfo = {
            'Name': $block.find('[name="contact_name"]').val(),
            'Email': $block.find('[name="contact_email"]').val(),
            'Phone': $block.find('[name="contact_phone"]').val() || 'Not provided',
            'Company': $block.find('[name="company_name"]').val() || 'Not specified',
            'Role': $block.find('[name="contact_role"] option:selected').text() || 'Not specified'
        };
        
        let contactInfoHtml = '';
        Object.entries(contactInfo).forEach(([key, value]) => {
            if (value && value !== 'Not provided' && value !== 'Not specified') {
                contactInfoHtml += `<p><strong>${key}:</strong> ${value}</p>`;
            }
        });
        $block.find('.contactInfoSummary').html(contactInfoHtml);
        
        // Additional Information
        const additionalInfo = {
            'Budget Range': $block.find('[name="budget_range"] option:selected').text() || 'Not specified',
            'Performance Duration': $block.find('[name="performance_duration"] option:selected').text() || 'Not specified',
            'Additional Info': $block.find('[name="additional_info"]').val() || 'None provided',
            'How did you hear about us': $block.find('[name="how_did_you_hear"] option:selected').text() || 'Not specified'
        };
        
        let additionalInfoHtml = '';
        Object.entries(additionalInfo).forEach(([key, value]) => {
            if (value && value !== 'Not specified' && value !== 'None provided') {
                additionalInfoHtml += `<p><strong>${key}:</strong> ${value}</p>`;
            }
        });
        $block.find('.additionalInfoSummary').html(additionalInfoHtml);
    }
    
    function submitForm() {
        // Collect all form data
        const formData = collectFormData();
        
        // Populate hidden Contact Form 7 fields
        populateContactForm7(formData);
        
        // Submit the Contact Form 7 form
        $block.find('.hiddenForm form').submit();
    }
    
    function collectFormData() {
        const data = {};
        
        // Selected artists
        const selectedArtists = [];
        $block.find('.artistOption input[type="checkbox"]:checked').each(function() {
            selectedArtists.push($(this).data('artist-name'));
        });
        data.selectedArtists = selectedArtists.join(', ');
        
        // All form fields
        $block.find('input, select, textarea').each(function() {
            const $field = $(this);
            const name = $field.attr('name');
            if (name && name !== 'selected_artists[]') {
                if ($field.is('select')) {
                    data[name] = $field.find('option:selected').text();
                } else {
                    data[name] = $field.val();
                }
            }
        });
        
        return data;
    }
    
    function populateContactForm7(data) {
        // This function would populate hidden fields in the Contact Form 7
        // The exact implementation depends on how the CF7 form is structured
        const $cf7Form = $block.find('.hiddenForm form');
        
        // Create hidden fields for all data
        Object.entries(data).forEach(([key, value]) => {
            if (value) {
                const $hiddenField = $('<input type="hidden" name="' + key + '" value="' + value + '">');
                $cf7Form.append($hiddenField);
            }
        });
    }
}
