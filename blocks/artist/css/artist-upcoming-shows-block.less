//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';
.artistUpcomingShowsBlock {
  margin: (@vw100 * 3) 0;
  .ShowsList {
    display: flex;
    flex-direction: column;
    gap: (@vw100 * 1.2);
    .ShowItem {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #181a3a;
      border-radius: (@vw100 * 0.7);
      padding: (@vw100 * 1) (@vw100 * 1.5);
      color: #fff;
      .ShowDate, .ShowLocation {
        font-size: (@vw100 * 1);
        font-weight: 500;
      }
      .ShowTickets {
        background: #fff;
        color: #181a3a;
        border-radius: (@vw100 * 0.5);
        padding: (@vw100 * 0.5) (@vw100 * 1.1);
        font-weight: bold;
        text-decoration: none;
        transition: background 0.2s;
        &:hover { background: #ff0; color: #181a3a; }
      }
    }
  }
}
