<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$bandsintown_id = get_field('bandsintown_id', $artist_id);
$artist_name = get_the_title($artist_id);

// Bepaal welke artist identifier te gebruiken
$artist_identifier = '';
if ($bandsintown_id) {
    // Als het een numeriek ID is, gebruik de artist naam voor de widget
    if (is_numeric($bandsintown_id)) {
        $artist_identifier = $artist_name;
    } else {
        $artist_identifier = $bandsintown_id;
    }
}
if ($artist_identifier): ?>
    <section class="artistUpcomingShowsBlock" data-init>
        <div class="contentWrapper smaller">
            <h2 class="upcomingShowsTitle">UPCOMING SHOWS</h2>

            <!-- Simple Bandsintown Link -->
            <div class="bandsintownSimple">
                <div class="showItem">
                    <div class="showInfo">
                        <div class="showDetails">
                            <div class="showLocation">CHECK UPCOMING SHOWS</div>
                            <div class="showVenue">View all upcoming tour dates and get tickets</div>
                        </div>
                    </div>
                    <div class="showTickets">
                        <a href="https://www.bandsintown.com/a/<?= esc_attr($bandsintown_id) ?>" target="_blank" rel="noopener" class="ticketsButton">
                            VIEW SHOWS
                        </a>
                    </div>
                </div>
            </div>

            <!-- Try to load widget with minimal config -->
            <div class="bandsintownWidget" style="margin-top: 20px;">
                <a class="bit-widget-initializer"
                   data-artist-name="<?= esc_attr($artist_identifier) ?>"
                   data-display-past-dates="false"
                   data-display-limit="5">
                </a>
            </div>
        </div>
    </section>

    <!-- Load widget script at the end -->
    <script>
    (function() {
        if (typeof window.BIT !== 'undefined') return;

        var script = document.createElement('script');
        script.src = 'https://widgetv3.bandsintown.com/main.min.js';
        script.async = true;
        script.onerror = function() {
            console.log('Bandsintown widget failed to load - using fallback link');
        };
        document.head.appendChild(script);
    })();
    </script>


<?php else: ?>
    <!-- Geen Bandsintown ID ingesteld -->
    <?php if (current_user_can('edit_posts')): ?>
        <section class="artistUpcomingShowsBlock" data-init>
            <div class="contentWrapper smaller">
                <h2 class="upcomingShowsTitle">UPCOMING SHOWS</h2>
                <div class="showsList">
                    <div class="noEventsMessage">
                        <p>No Bandsintown ID set for this artist.</p>
                        <p><small>Please add a Bandsintown ID in the artist edit page. This message is only visible to editors.</small></p>
                    </div>
                </div>
            </div>
        </section>
    <?php endif;
endif;
