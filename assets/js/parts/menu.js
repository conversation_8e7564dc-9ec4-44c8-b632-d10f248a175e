var menuIsOpen = false;
var defaultMenuWidth;
var defaultMenuHeight;

var container;
var oldHeight;
var newHeight;

var menuDisabled = false;

document.fonts.ready.then(function(){
  var menu = $("#menu");
  gsap.registerPlugin(CustomEase);
  CustomEase.create(
    "menubackground",
    "M0,0 C0.83,0 0.17,1 1,1"
  );

  defaultMenuWidth = $("header #menu").outerWidth();
  defaultMenuHeight = $("header #menu").outerHeight();


  gsap.to("header #menu .background", 0, {
    width: defaultMenuWidth,
    height: defaultMenuHeight
  });

  $(window).on("resize", function(){
    defaultMenuWidth = $("header #menu").outerWidth();
    defaultMenuHeight = $("header #menu").outerHeight();

    gsap.to("header #menu .background", 0, {
      width: $("header #menu").outerWidth(),
      height: $("header #menu").outerHeight()
    });
  });

  $(document).on("click", "#burger, #menu .hamburger, .blurredOverlay, #menu .innerContent li a", function(e){
    if (!menuDisabled) {
      menuDisabled = true;
      if (menuIsOpen) {
        menuIsOpen = false;
        closeMenu();
      } else {
        openMenu();
        menuIsOpen = true;
      }
    }
  });


  setTimeout(function () {
    let lastScrollY = 0;
    let lastTime = performance.now();
    
    scroller.on("scroll", function () {
      if (!menuDisabled && menuIsOpen) {
        let currentScrollY = window.scrollY;
        let currentTime = performance.now();
        
        let scrollSpeed = Math.abs(currentScrollY - lastScrollY) / (currentTime - lastTime);
        
        lastScrollY = currentScrollY;
        lastTime = currentTime;
        if (scrollSpeed > 0.1) { // Pas deze drempel aan indien nodig
          menuIsOpen = false;
          closeMenu("header #menu");
        }
      }
    });
  }, 400);
  $(document).on("blur", "#pageContainer", function(e){
    if (!menuDisabled) {
      if (menuIsOpen) {
        menuIsOpen = false;
        closeMenu("header #menu");
      }
    }
  });

  $(document).on("mouseover", "header #menu .menu-primary-menu-container li", function(e){
    var item = $(e)[0].currentTarget
    $(item).parents("ul").find("li").removeClass("active");
    $(item).parents("ul").addClass("hover");
    $(item).addClass("active");
  });

  $(document).on("mouseleave", "header #menu .menu-primary-menu-container li", function(e){
    var item = $(e)[0].currentTarget
    $(item).parents("ul").find("li").removeClass("active");
    $(item).parents("ul").removeClass("hover");
  });
});

function openMenu() {
  $(menu).toggleClass("active");
  $(".blurredOverlay").toggleClass("active");
  $(menu).parents("header").find(".socials").toggleClass("active");
  gsap.to($(menu).find(".background"), .9, {
    width: $(menu).outerWidth(),
    height: $(menu).outerHeight(),
    ease: "menubackground"
  });
  setTimeout(function(){
    $(menu).find(".innerContent").addClass("showContent");
  }, 600);
  setTimeout(function(){
    menuDisabled = false;
  }, 900);
}

function closeMenu() {
  $(menu).find(".innerContent").removeClass("showContent");
  setTimeout(function(){
    gsap.to($(menu).find(".background"), .3, {
      width: defaultMenuWidth,
      height: defaultMenuHeight,
      ease: "menubackground"
    });
    $(menu).toggleClass("active");
    $(".blurredOverlay").toggleClass("active");
    $(menu).parents("header").find(".socials").toggleClass("active");
  }, 300);
  setTimeout(function(){
    menuDisabled = false;
  }, 600);
}
