@import '../../../assets/less/constants.less';
@import '../../../assets/less/vw_values.less';

.artistMediaBlock {
  padding: @vw80 0;
  background: transparent;
  position: relative;

  .mediaTitle {
    font-family: 'ApexMk2-BoldExtended', Arial, sans-serif;
    font-weight: bold;
    font-size: @vw32;
    color: @hardWhite;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: @vw60;
  }

  .mediaContainer {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: @vw40;
    align-items: start;
  }
  
  .youtubeContainer {
    .videoWrapper {
      position: relative;
      width: 100%;
      height: 0;
      padding-bottom: 56.25%; // 16:9 aspect ratio
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      .rounded(@vw15);
      overflow: hidden;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(255, 255, 255, 0.2);
        .transform(translateY(-2px));
        .box-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
      }
      
      iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
        .rounded(@vw15);
      }
    }
  }
  
  .spotifyContainer {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    .rounded(@vw15);
    overflow: hidden;
    transition: all 0.3s ease;
    height: 352px; // Match Spotify embed height
    
    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.2);
      .transform(translateY(-2px));
      .box-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
    }
    
    iframe {
      width: 100%;
      height: 100%;
      border: none;
      .rounded(@vw15);
      background: transparent;
    }
  }
  
  .noMediaMessage {
    text-align: center;
    padding: @vw40;
    background: rgba(255, 255, 255, 0.05);
    .rounded(@vw15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    p {
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: @vw10;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      small {
        font-size: @vw14;
        opacity: 0.8;
      }
    }
  }
  
  // Responsive adjustments
  @media all and (max-width: 1160px) {
    padding: @vw60 0;

    .mediaContainer {
      gap: @vw30;
    }

    .mediaTitle {
      font-size: @vw28;
      margin-bottom: @vw40;
    }
  }

  @media all and (max-width: 580px) {
    padding: @vw40 0;

    .mediaTitle {
      font-size: @vw24;
      margin-bottom: @vw30;
    }

    .mediaContainer {
      grid-template-columns: 1fr;
      gap: @vw20;
    }

    .spotifyContainer {
      height: 300px;
    }
  }
  
  // Animation on scroll
  &[data-init] {
    opacity: 0;
    .transform(translateY(30px));
    transition: all 0.8s ease;
    
    &.in-view {
      opacity: 1;
      .transform(translateY(0));
    }
  }
  
  // Custom scrollbar for Spotify iframe (if visible)
  .spotifyContainer iframe {
    scrollbar-width: thin;
    scrollbar-color: @secondaryColor rgba(255, 255, 255, 0.1);
    
    &::-webkit-scrollbar {
      width: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      .rounded(4px);
    }
    
    &::-webkit-scrollbar-thumb {
      background: @secondaryColor;
      .rounded(4px);
      
      &:hover {
        background: @secondaryColorLight;
      }
    }
  }
}
