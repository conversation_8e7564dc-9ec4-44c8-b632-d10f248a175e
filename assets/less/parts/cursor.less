// out: false
@import '../vw_values.less';
@import '../constants.less';

.mainCursor {
    position: fixed;
    width: 50vw;
    height: 50vw;
    background: @secondaryColorLight;
    pointer-events: none;
    opacity: .5;
    .filter(blur(@vw50));
    transform: translate(-50%, -50%);
    pointer-events: none;
    .rounded(50%);
    -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
}