<?php
$title = get_field('title');
$subtitle = get_field('subtitle');
$text = get_field('text');
$contact_form_id = get_field('contact_form_id');

// Haal alle artiesten op voor de selectie
$artists = new WP_Query([
    'post_type' => 'artist',
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'orderby' => 'title',
    'order' => 'ASC',
    'meta_query' => [
        [
            'key' => 'image',
            'compare' => 'EXISTS',
        ],
    ],
]);
?>

<section class="bookingRequestBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="formHeader">
            <?php if ($subtitle): ?>
                <div class="tinyTitle"><?php echo esc_html($subtitle); ?></div>
            <?php endif; ?>
            <?php if ($title): ?>
                <h2 class="biggerTitle"><?php echo esc_html($title); ?></h2>
            <?php endif; ?>
            <?php if ($text): ?>
                <div class="text"><p><?php echo esc_html($text); ?></p></div>
            <?php endif; ?>
        </div>

        <div class="progressIndicator">
            <div class="progressBar">
                <div class="progressFill"></div>
            </div>
            <div class="stepIndicators">
                <div class="stepIndicator active" data-step="1">
                    <span class="stepNumber">1</span>
                    <span class="stepLabel">Artists</span>
                </div>
                <div class="stepIndicator" data-step="2">
                    <span class="stepNumber">2</span>
                    <span class="stepLabel">Event Details</span>
                </div>
                <div class="stepIndicator" data-step="3">
                    <span class="stepNumber">3</span>
                    <span class="stepLabel">Contact Info</span>
                </div>
                <div class="stepIndicator" data-step="4">
                    <span class="stepNumber">4</span>
                    <span class="stepLabel">Budget & Info</span>
                </div>
                <div class="stepIndicator" data-step="5">
                    <span class="stepNumber">5</span>
                    <span class="stepLabel">Review & Submit</span>
                </div>
            </div>
        </div>

        <div class="formSteps">
            <!-- Step 1: Artist Selection -->
            <div class="formStep active" data-step="1">
                <h3 class="stepTitle">Select Artists</h3>
                <p class="stepDescription">Choose the artists you would like to book for your event.</p>
                
                <div class="artistSelection">
                    <?php if ($artists->have_posts()): ?>
                        <?php foreach ($artists->posts as $post): 
                            setup_postdata($post);
                            $title = get_the_title($post->ID);
                            $image = get_field('image', $post->ID);
                            $roster_type = get_field('roster_type', $post->ID);
                        ?>
                            <div class="artistOption" data-roster-type="<?php echo esc_attr($roster_type); ?>">
                                <label class="artistLabel">
                                    <input type="checkbox" name="selected_artists[]" value="<?php echo esc_attr($post->ID); ?>" data-artist-name="<?php echo esc_attr($title); ?>">
                                    <div class="artistCard">
                                        <?php if ($image): ?>
                                            <div class="artistImage">
                                                <img src="<?php echo esc_url($image['sizes']['medium']); ?>" alt="<?php echo esc_attr($title); ?>">
                                            </div>
                                        <?php endif; ?>
                                        <div class="artistInfo">
                                            <h4 class="artistName"><?php echo esc_html($title); ?></h4>
                                            <span class="artistType"><?php echo $roster_type === 'ones_to_watch' ? 'Ones to Watch' : 'Main Roster'; ?></span>
                                        </div>
                                        <div class="checkmark"></div>
                                    </div>
                                </label>
                            </div>
                        <?php endforeach; ?>
                        <?php wp_reset_postdata(); ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Step 2: Event Details -->
            <div class="formStep" data-step="2">
                <h3 class="stepTitle">Event Details</h3>
                <p class="stepDescription">Tell us about your event.</p>
                
                <div class="formFields">
                    <div class="fieldGroup">
                        <label for="event_date">Event Date *</label>
                        <input type="date" id="event_date" name="event_date" required>
                    </div>
                    
                    <div class="fieldGroup">
                        <label for="event_location">Event Location *</label>
                        <input type="text" id="event_location" name="event_location" placeholder="City, Country" required>
                    </div>
                    
                    <div class="fieldGroup">
                        <label for="venue_name">Venue Name</label>
                        <input type="text" id="venue_name" name="venue_name" placeholder="Name of the venue">
                    </div>
                    
                    <div class="fieldGroup">
                        <label for="event_type">Event Type *</label>
                        <select id="event_type" name="event_type" required>
                            <option value="">Select event type</option>
                            <option value="festival">Festival</option>
                            <option value="club">Club Event</option>
                            <option value="private">Private Event</option>
                            <option value="corporate">Corporate Event</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="fieldGroup">
                        <label for="expected_attendance">Expected Attendance</label>
                        <select id="expected_attendance" name="expected_attendance">
                            <option value="">Select attendance</option>
                            <option value="0-100">0-100 people</option>
                            <option value="100-500">100-500 people</option>
                            <option value="500-1000">500-1000 people</option>
                            <option value="1000-5000">1000-5000 people</option>
                            <option value="5000+">5000+ people</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Step 3: Contact Information -->
            <div class="formStep" data-step="3">
                <h3 class="stepTitle">Contact Information</h3>
                <p class="stepDescription">How can we reach you?</p>
                
                <div class="formFields">
                    <div class="fieldGroup">
                        <label for="contact_name">Full Name *</label>
                        <input type="text" id="contact_name" name="contact_name" required>
                    </div>
                    
                    <div class="fieldGroup">
                        <label for="contact_email">Email Address *</label>
                        <input type="email" id="contact_email" name="contact_email" required>
                    </div>
                    
                    <div class="fieldGroup">
                        <label for="contact_phone">Phone Number</label>
                        <input type="tel" id="contact_phone" name="contact_phone">
                    </div>
                    
                    <div class="fieldGroup">
                        <label for="company_name">Company/Organization</label>
                        <input type="text" id="company_name" name="company_name">
                    </div>
                    
                    <div class="fieldGroup">
                        <label for="contact_role">Your Role</label>
                        <select id="contact_role" name="contact_role">
                            <option value="">Select your role</option>
                            <option value="event_organizer">Event Organizer</option>
                            <option value="booking_agent">Booking Agent</option>
                            <option value="promoter">Promoter</option>
                            <option value="venue_manager">Venue Manager</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Step 4: Budget & Additional Information -->
            <div class="formStep" data-step="4">
                <h3 class="stepTitle">Budget & Additional Information</h3>
                <p class="stepDescription">Help us understand your requirements better.</p>
                
                <div class="formFields">
                    <div class="fieldGroup">
                        <label for="budget_range">Budget Range</label>
                        <select id="budget_range" name="budget_range">
                            <option value="">Select budget range</option>
                            <option value="under_5k">Under €5,000</option>
                            <option value="5k_10k">€5,000 - €10,000</option>
                            <option value="10k_25k">€10,000 - €25,000</option>
                            <option value="25k_50k">€25,000 - €50,000</option>
                            <option value="50k_plus">€50,000+</option>
                            <option value="discuss">Prefer to discuss</option>
                        </select>
                    </div>
                    
                    <div class="fieldGroup">
                        <label for="performance_duration">Performance Duration</label>
                        <select id="performance_duration" name="performance_duration">
                            <option value="">Select duration</option>
                            <option value="30min">30 minutes</option>
                            <option value="1hour">1 hour</option>
                            <option value="1.5hours">1.5 hours</option>
                            <option value="2hours">2 hours</option>
                            <option value="longer">Longer</option>
                        </select>
                    </div>
                    
                    <div class="fieldGroup full-width">
                        <label for="additional_info">Additional Information</label>
                        <textarea id="additional_info" name="additional_info" rows="4" placeholder="Tell us more about your event, special requirements, or any other details..."></textarea>
                    </div>
                    
                    <div class="fieldGroup full-width">
                        <label for="how_did_you_hear">How did you hear about us?</label>
                        <select id="how_did_you_hear" name="how_did_you_hear">
                            <option value="">Select option</option>
                            <option value="website">Website</option>
                            <option value="social_media">Social Media</option>
                            <option value="referral">Referral</option>
                            <option value="event">At an event</option>
                            <option value="search_engine">Search Engine</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Step 5: Review & Submit -->
            <div class="formStep" data-step="5">
                <h3 class="stepTitle">Review & Submit</h3>
                <p class="stepDescription">Please review your booking request before submitting.</p>
                
                <div class="reviewSummary">
                    <div class="summarySection">
                        <h4>Selected Artists</h4>
                        <div class="selectedArtistsList"></div>
                    </div>
                    
                    <div class="summarySection">
                        <h4>Event Details</h4>
                        <div class="eventDetailsSummary"></div>
                    </div>
                    
                    <div class="summarySection">
                        <h4>Contact Information</h4>
                        <div class="contactInfoSummary"></div>
                    </div>
                    
                    <div class="summarySection">
                        <h4>Additional Information</h4>
                        <div class="additionalInfoSummary"></div>
                    </div>
                </div>
                
                <!-- Hidden Contact Form 7 form -->
                <div class="hiddenForm">
                    <?php if ($contact_form_id): ?>
                        <?php echo do_shortcode('[contact-form-7 id="' . $contact_form_id . '"]'); ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="formNavigation">
            <button type="button" class="navButton prevButton" disabled>
                <span class="arrows"><i class="icon-chevron-left"></i><i class="icon-chevron-left"></i></span>
                <span class="buttonText">Previous</span>
            </button>
            
            <button type="button" class="navButton nextButton">
                <span class="buttonText">Next</span>
                <span class="arrows"><i class="icon-chevron-right"></i><i class="icon-chevron-right"></i></span>
            </button>
            
            <button type="button" class="navButton submitButton" style="display: none;">
                <span class="buttonText">Submit Request</span>
                <span class="arrows"><i class="icon-arrow-right-up"></i><i class="icon-arrow-right-up"></i></span>
            </button>
        </div>
    </div>
</section>
