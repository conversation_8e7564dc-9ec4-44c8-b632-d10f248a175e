.artistMediaBlock {
  padding: 4.63vw 0;
  background: transparent;
  position: relative;
}
.artistMediaBlock .mediaTitle {
  font-family: 'ApexMk2-BoldExtended', Arial, sans-serif;
  font-weight: bold;
  font-size: 1.852vw;
  color: #FFFFFF;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 3.472vw;
}
.artistMediaBlock .mediaContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2.315vw;
  align-items: start;
}
.artistMediaBlock .youtubeContainer .videoWrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-border-radius: 0.868vw;
  -moz-border-radius: 0.868vw;
  border-radius: 0.868vw;
  overflow: hidden;
  transition: all 0.3s ease;
}
.artistMediaBlock .youtubeContainer .videoWrapper:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  -webkit-transform: translateY(-2px);
  -moz-transform: translateY(-2px);
  -o-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
  -webkit-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}
.artistMediaBlock .youtubeContainer .videoWrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  -webkit-border-radius: 0.868vw;
  -moz-border-radius: 0.868vw;
  border-radius: 0.868vw;
}
.artistMediaBlock .spotifyContainer {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-border-radius: 0.868vw;
  -moz-border-radius: 0.868vw;
  border-radius: 0.868vw;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 352px;
}
.artistMediaBlock .spotifyContainer:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  -webkit-transform: translateY(-2px);
  -moz-transform: translateY(-2px);
  -o-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px);
  -webkit-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}
.artistMediaBlock .spotifyContainer iframe {
  width: 100%;
  height: 100%;
  border: none;
  -webkit-border-radius: 0.868vw;
  -moz-border-radius: 0.868vw;
  border-radius: 0.868vw;
  background: transparent;
}
.artistMediaBlock .noMediaMessage {
  text-align: center;
  padding: 2.315vw;
  background: rgba(255, 255, 255, 0.05);
  -webkit-border-radius: 0.868vw;
  -moz-border-radius: 0.868vw;
  border-radius: 0.868vw;
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.artistMediaBlock .noMediaMessage p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.579vw;
}
.artistMediaBlock .noMediaMessage p:last-child {
  margin-bottom: 0;
}
.artistMediaBlock .noMediaMessage p small {
  font-size: 0.81vw;
  opacity: 0.8;
}
@media all and (max-width: 1160px) {
  .artistMediaBlock {
    padding: 3.472vw 0;
  }
  .artistMediaBlock .mediaContainer {
    gap: 1.736vw;
  }
  .artistMediaBlock .mediaTitle {
    font-size: 1.62vw;
    margin-bottom: 2.315vw;
  }
}
@media all and (max-width: 580px) {
  .artistMediaBlock {
    padding: 2.315vw 0;
  }
  .artistMediaBlock .mediaTitle {
    font-size: 1.389vw;
    margin-bottom: 1.736vw;
  }
  .artistMediaBlock .mediaContainer {
    grid-template-columns: 1fr;
    gap: 1.157vw;
  }
  .artistMediaBlock .spotifyContainer {
    height: 300px;
  }
}
.artistMediaBlock[data-init] {
  opacity: 0;
  -webkit-transform: translateY(30px);
  -moz-transform: translateY(30px);
  -o-transform: translateY(30px);
  -ms-transform: translateY(30px);
  transform: translateY(30px);
  transition: all 0.8s ease;
}
.artistMediaBlock[data-init].in-view {
  opacity: 1;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -o-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}
.artistMediaBlock .spotifyContainer iframe {
  scrollbar-width: thin;
  scrollbar-color: #5A51A3 rgba(255, 255, 255, 0.1);
}
.artistMediaBlock .spotifyContainer iframe::-webkit-scrollbar {
  width: 8px;
}
.artistMediaBlock .spotifyContainer iframe::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.artistMediaBlock .spotifyContainer iframe::-webkit-scrollbar-thumb {
  background: #5A51A3;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.artistMediaBlock .spotifyContainer iframe::-webkit-scrollbar-thumb:hover {
  background: #D4D0ED;
}
