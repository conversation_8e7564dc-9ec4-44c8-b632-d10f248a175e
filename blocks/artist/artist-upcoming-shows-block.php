<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$bandsintown_id = get_field('bandsintown_id', $artist_id);

if ($bandsintown_id):
    $events = get_artist_bandsintown_events($bandsintown_id);

    // Filter en sorteer events
    $upcoming_events = array();
    if ($events && is_array($events)) {
        foreach ($events as $event) {
            if (is_array($event) && isset($event['datetime'], $event['venue']) && is_array($event['venue'])) {
                // Alleen toekomstige events
                if (strtotime($event['datetime']) > time()) {
                    $upcoming_events[] = $event;
                }
            }
        }

        // Sorteer op datum
        usort($upcoming_events, function($a, $b) {
            return strtotime($a['datetime']) - strtotime($b['datetime']);
        });
    }

    if (!empty($upcoming_events)): ?>
        <section class="artistUpcomingShowsBlock" data-init>
            <div class="contentWrapper smaller">
                <h2 class="upcomingShowsTitle">UPCOMING SHOWS</h2>
                <div class="showsList">
                    <?php foreach ($upcoming_events as $event):
                        $date = new DateTime($event['datetime']);
                        $venue = $event['venue'];
                        $city = $venue['city'] ?? '';
                        $region = $venue['region'] ?? '';
                        $country = $venue['country'] ?? '';
                        $venue_name = $venue['name'] ?? '';

                        // Format location
                        $location_parts = array_filter([$city, $region]);
                        $location = implode(', ', $location_parts);
                        if ($country && $country !== 'United States') {
                            $location .= ', ' . $country;
                        }

                        // Ticket URL (if available)
                        $ticket_url = $event['url'] ?? $event['ticket_url'] ?? '#';

                        // Skip if no valid ticket URL
                        if ($ticket_url === '#' || empty($ticket_url)) {
                            $ticket_url = 'https://www.bandsintown.com/a/' . urlencode($bandsintown_id);
                        }
                    ?>
                        <div class="showItem">
                            <div class="showInfo">
                                <div class="showDate">
                                    <?= esc_html($date->format('d/m/y')) ?>
                                </div>
                                <div class="showDetails">
                                    <div class="showLocation"><?= esc_html(strtoupper($location)) ?></div>
                                    <div class="showVenue"><?= esc_html(strtoupper($venue_name)) ?></div>
                                </div>
                            </div>
                            <div class="showTickets">
                                <a href="<?= esc_url($ticket_url) ?>" target="_blank" rel="noopener" class="ticketsButton">
                                    TICKETS
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    <?php else: ?>
        <!-- Optionally show a message when no events are found -->
        <?php if (current_user_can('edit_posts')): ?>
            <section class="artistUpcomingShowsBlock" data-init>
                <div class="contentWrapper smaller">
                    <h2 class="upcomingShowsTitle">UPCOMING SHOWS</h2>
                    <div class="showsList">
                        <div class="noEventsMessage">
                            <p>No upcoming shows found. Make sure the Bandsintown ID (<?= esc_html($bandsintown_id) ?>) is correct.</p>
                            <p><small>This message is only visible to editors.</small></p>
                        </div>
                    </div>
                </div>
            </section>
        <?php endif; ?>
    <?php endif;
endif;
