.artistMediaBlock {
  padding: 4.63vw 0;
  background: transparent;
  position: relative;
}
.artistMediaBlock .normalTitle {
  text-align: left;
  margin-bottom: 3.472vw;
}
.artistMediaBlock .mediaContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2.315vw;
  align-items: flex-start;
}

.artistMediaBlock .youtubeContainer .videoWrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  overflow: hidden;
}
.artistMediaBlock .youtubeContainer .videoWrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}
.artistMediaBlock .spotifyContainer {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  overflow: hidden;
}
.artistMediaBlock .spotifyContainer iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}
.artistMediaBlock .noMediaMessage {
  text-align: center;
  padding: 2.315vw;
}
.artistMediaBlock .noMediaMessage p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.579vw;
}
.artistMediaBlock .noMediaMessage p:last-child {
  margin-bottom: 0;
}
.artistMediaBlock .noMediaMessage p small {
  font-size: 0.81vw;
  opacity: 0.8;
}
@media all and (max-width: 580px) {
  .artistMediaBlock .mediaContainer {
    grid-template-columns: 1fr;
    gap: 1.736vw;
  }
}
