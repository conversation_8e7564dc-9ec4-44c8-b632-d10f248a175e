$(document).ready(function(){
    $(document).on("initPage", function () {
      if ($(".introTextBlock").length > 0) {
        setTimeout(function(){
          initializeIntroTextBlock();
        }, 450);
      }
    });
});

function initializeIntroTextBlock() {
  $(window).on("scroll", function(){
      if ($("[data-anim-text]").length > 0) {
          animateTextOnScroll();
      }
  });
}

function setupTextLineWrappers(){
    $(".animTextWrapper").each(function(idx, wrapperEl){
        if (!$(wrapperEl).find(".line").length > 0) {
            $(wrapperEl).find(".animText").splitLines({
                tag: '<div class="row"><span class="innerRow">',
                keepHtml: true
            });
            var overlayClone = $(wrapperEl).find(".animText").clone().addClass("overlay");
            $(wrapperEl).append(overlayClone);
            $(wrapperEl).find(".animText.overlay .innerRow").each(function(i, overlayRow){
                $(overlayRow).width($(".animText:not(.overlay) .innerRow").eq(i).width());
            });
        } 
    }); 
}

function animateTextOnScroll(){
    $("[data-anim-text]").each(function(idx, animEl){
        if($(animEl).length > 0){
            if($(animEl).offset().top - 50 - scrollY - $(window).height() < 0 && $(animEl).offset().top - scrollY > 0){
                $(animEl).data("progress", Math.abs($(animEl).offset().top - scrollY - $(window).height()) / ($(window).height()) * 100);
            } else if($(animEl).offset().top - scrollY- $(window).height() > 0){
                $(animEl).data("progress", 0);
            } else if($(animEl).offset().top - scrollY < 0){
                $(animEl).data("progress", 100);
            }
            var totalLines = $(animEl).find("[data-lines]:not(.overlayText) .line").length;
            var progress = $(animEl).data("progress");
            var progressPerLine = 100 / totalLines;
            $(animEl).find("[data-lines].overlayText .line").each(function(i, lineEl){
                var progressLine = progress * totalLines;
                if(progressLine - (100 * i) >= 100){
                    gsap.to(lineEl, .3, {scaleX: "100%"});
                } else {
                    if(i * progressPerLine <= progress){
                        gsap.to(lineEl, .3, {scaleX: (progressLine - (100 * i)) + "%"});
                    } else {
                        gsap.to(lineEl, .3, {scaleX: "0%"});
                    }
                }
            });
        }
    });
}
