<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$bandsintown_id = get_field('bandsintown_id', $artist_id);
$artist_name = get_the_title($artist_id);

// Bepaal welke artist identifier te gebruiken
$artist_identifier = '';
if ($bandsintown_id) {
    // Als het een numeriek ID is, gebruik de artist naam voor de widget
    if (is_numeric($bandsintown_id)) {
        $artist_identifier = $artist_name;
    } else {
        $artist_identifier = $bandsintown_id;
    }
}
if ($artist_identifier): ?>
    <section class="artistUpcomingShowsBlock" data-init>
        <div class="contentWrapper smaller">
            <h2 class="upcomingShowsTitle">UPCOMING SHOWS</h2>

            <!-- Bandsintown Events Widget -->
            <div class="bandsintownWidget">
                <a class="bit-widget-initializer"
                   data-artist-name="<?= esc_attr($artist_identifier) ?>"
                   data-display-local-dates="false"
                   data-display-past-dates="false"
                   data-auto-style="false"
                   data-text-color="#FFFFFF"
                   data-link-color="#00b4b3"
                   data-link-text-color="#FFFFFF"
                   data-popup-background-color="#1a1a1a"
                   data-background-color="transparent"
                   data-separator-color="rgba(255,255,255,0.1)"
                   data-display-limit="10"
                   data-display-lineup="false"
                   data-display-logo="false"
                   data-display-track-button="false"
                   data-widget-width="100%"
                   data-language="en"
                   data-app-id="limitless_agency">
                </a>
            </div>

            <!-- Fallback message voor als widget niet laadt -->
            <div class="widgetFallback" style="display: none;">
                <div class="noEventsMessage">
                    <p>Unable to load upcoming shows. Please visit our <a href="https://www.bandsintown.com/a/<?= esc_attr($bandsintown_id) ?>" target="_blank" rel="noopener">Bandsintown page</a> for the latest tour dates.</p>
                </div>
            </div>
        </div>
    </section>
    <!-- Bandsintown Widget Script -->
    <script charset="utf-8" src="https://widgetv3.bandsintown.com/main.min.js"></script>

    <script>
    // Custom styling en error handling voor de widget
    document.addEventListener('DOMContentLoaded', function() {
        const widget = document.querySelector('.bit-widget-initializer');
        const fallback = document.querySelector('.widgetFallback');

        if (widget) {
            // Timeout om fallback te tonen als widget niet laadt
            setTimeout(function() {
                const widgetContent = document.querySelector('.bit-widget');
                if (!widgetContent || widgetContent.children.length === 0) {
                    if (fallback) {
                        fallback.style.display = 'block';
                    }
                }
            }, 5000);

            // Observer om te detecteren wanneer widget geladen is
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        const widgetContent = document.querySelector('.bit-widget');
                        if (widgetContent && widgetContent.children.length > 0) {
                            // Widget is geladen, voeg custom styling toe
                            addCustomWidgetStyling();
                            observer.disconnect();
                        }
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    });

    function addCustomWidgetStyling() {
        // Voeg custom CSS toe om de widget te stylen zoals jouw design
        const style = document.createElement('style');
        style.textContent = `
            .bit-widget {
                font-family: 'ApexMk2-BoldExtended', 'ApexMk2-Regular', Arial, sans-serif !important;
            }

            .bit-widget .bit-event {
                background: rgba(255, 255, 255, 0.05) !important;
                backdrop-filter: blur(10px) !important;
                border: 1px solid rgba(255, 255, 255, 0.1) !important;
                border-radius: 15px !important;
                margin-bottom: 20px !important;
                padding: 25px 30px !important;
                transition: all 0.3s ease !important;
            }

            .bit-widget .bit-event:hover {
                background: rgba(255, 255, 255, 0.08) !important;
                border-color: rgba(255, 255, 255, 0.2) !important;
                transform: translateY(-2px) !important;
            }

            .bit-widget .bit-event-date {
                font-family: 'ApexMk2-BoldExtended', Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 24px !important;
                color: #FFFFFF !important;
            }

            .bit-widget .bit-event-location {
                font-family: 'ApexMk2-BoldExtended', Arial, sans-serif !important;
                font-weight: bold !important;
                font-size: 18px !important;
                color: #00b4b3 !important;
                text-transform: uppercase !important;
                letter-spacing: 1px !important;
            }

            .bit-widget .bit-event-venue {
                font-family: 'ApexMk2-Regular', Arial, sans-serif !important;
                font-size: 16px !important;
                color: rgba(255, 255, 255, 0.8) !important;
                text-transform: uppercase !important;
                letter-spacing: 0.5px !important;
            }

            .bit-widget .bit-event-button {
                background: #00b4b3 !important;
                color: #FFFFFF !important;
                padding: 12px 25px !important;
                border-radius: 8px !important;
                font-family: 'ApexMk2-BoldExtended', Arial, sans-serif !important;
                font-size: 14px !important;
                font-weight: bold !important;
                letter-spacing: 1px !important;
                text-transform: uppercase !important;
                transition: all 0.3s ease !important;
                border: none !important;
            }

            .bit-widget .bit-event-button:hover {
                background: #00d4d3 !important;
                transform: scale(1.05) !important;
            }
        `;
        document.head.appendChild(style);
    }
    </script>

<?php else: ?>
    <!-- Geen Bandsintown ID ingesteld -->
    <?php if (current_user_can('edit_posts')): ?>
        <section class="artistUpcomingShowsBlock" data-init>
            <div class="contentWrapper smaller">
                <h2 class="upcomingShowsTitle">UPCOMING SHOWS</h2>
                <div class="showsList">
                    <div class="noEventsMessage">
                        <p>No Bandsintown ID set for this artist.</p>
                        <p><small>Please add a Bandsintown ID in the artist edit page. This message is only visible to editors.</small></p>
                    </div>
                </div>
            </div>
        </section>
    <?php endif;
endif;
                        <p><small>Please add a Bandsintown ID in the artist edit page. This message is only visible to editors.</small></p>
                    </div>
                </div>
            </div>
        </section>
    <?php endif;
endif;
