.artistMediaBlock {
  padding: 4.63vw 0;
  background: transparent;
  position: relative;
}
.artistMediaBlock.inview .mediaContainer .youtubeContainer,
.artistMediaBlock.inview .mediaContainer .spotifyContainer {
  opacity: 1;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -o-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  transition: opacity 0.6s 0.2s ease-out, transform 0.6s 0.2s ease-out;
}
.artistMediaBlock.inview .mediaContainer .youtubeContainer:nth-child(2),
.artistMediaBlock.inview .mediaContainer .spotifyContainer:nth-child(2) {
  transition-delay: 0.3s;
}
.artistMediaBlock.inview .mediaContainer .youtubeContainer:nth-child(1),
.artistMediaBlock.inview .mediaContainer .spotifyContainer:nth-child(1) {
  transition-delay: 0.15s;
}
.artistMediaBlock .normalTitle {
  text-align: left;
  margin-bottom: 1.505vw;
}
.artistMediaBlock .mediaContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2.315vw;
  align-items: flex-start;
}
.artistMediaBlock .youtubeContainer,
.artistMediaBlock .spotifyContainer {
  opacity: 0;
  -webkit-transform: translateY(2.894vw);
  -moz-transform: translateY(2.894vw);
  -o-transform: translateY(2.894vw);
  -ms-transform: translateY(2.894vw);
  transform: translateY(2.894vw);
}
.artistMediaBlock .youtubeContainer .videoWrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  overflow: hidden;
}
.artistMediaBlock .youtubeContainer .videoWrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}
.artistMediaBlock .spotifyContainer {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  overflow: hidden;
}
.artistMediaBlock .spotifyContainer iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}
.artistMediaBlock .noMediaMessage {
  text-align: center;
  padding: 2.315vw;
  background: rgba(255, 255, 255, 0.05);
  -webkit-border-radius: 0.868vw;
  -moz-border-radius: 0.868vw;
  border-radius: 0.868vw;
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.artistMediaBlock .noMediaMessage p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.579vw;
}
.artistMediaBlock .noMediaMessage p:last-child {
  margin-bottom: 0;
}
.artistMediaBlock .noMediaMessage p small {
  font-size: 0.81vw;
  opacity: 0.8;
}
@media all and (max-width: 1160px) {
  .artistMediaBlock {
    padding: 3.472vw 0;
  }
  .artistMediaBlock .mediaContainer {
    gap: 1.736vw;
  }
  .artistMediaBlock .mediaTitle {
    font-size: 1.62vw;
    margin-bottom: 2.315vw;
  }
}
@media all and (max-width: 580px) {
  .artistMediaBlock {
    padding: 2.315vw 0;
  }
  .artistMediaBlock .mediaTitle {
    font-size: 1.389vw;
    margin-bottom: 1.736vw;
  }
  .artistMediaBlock .mediaContainer {
    grid-template-columns: 1fr;
    gap: 1.157vw;
  }
  .artistMediaBlock .spotifyContainer {
    height: 300px;
  }
}
.artistMediaBlock .spotifyContainer iframe {
  scrollbar-width: thin;
  scrollbar-color: #5A51A3 rgba(255, 255, 255, 0.1);
}
.artistMediaBlock .spotifyContainer iframe::-webkit-scrollbar {
  width: 8px;
}
.artistMediaBlock .spotifyContainer iframe::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.artistMediaBlock .spotifyContainer iframe::-webkit-scrollbar-thumb {
  background: #5A51A3;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
.artistMediaBlock .spotifyContainer iframe::-webkit-scrollbar-thumb:hover {
  background: #D4D0ED;
}
