//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';
.artistTwoColumnTextBlock {
  .cols {
    width: calc(100% ~"+" @vw16);
    margin-left: -@vw8;
    .col {
      display: inline-block;
      margin: 0 @vw8;
      width: calc(50% ~"-" @vw16);
      vertical-align: top;
      &:not(:first-child) {
        opacity: .7;
      }
    }
  }
  p {
    &:not(:last-child) {
      margin-bottom: @vw22;
    }
  }
}